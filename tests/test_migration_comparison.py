#!/usr/bin/env python3
"""
渐进式迁移测试
对比新旧实现的功能一致性，确保迁移到 create_react_agent 后功能不回退
"""

import asyncio
import sys
import os
import pytest
from unittest.mock import patch, AsyncMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.tools import tool


class TestMigrationComparison:
    """迁移对比测试"""
    
    @pytest.fixture
    def mock_tools(self):
        """创建模拟工具"""
        @tool
        def test_tool(query: str) -> str:
            """测试工具"""
            return f"测试结果: {query}"
        
        return [test_tool]
    
    @pytest.fixture
    def mock_llm(self):
        """创建模拟 LLM"""
        from ollama_adapter import OllamaChatModel
        
        # 创建模拟的 LLM
        llm = OllamaChatModel(
            model="test_model",
            base_url="http://localhost:11434"
        )
        
        # 模拟 invoke 方法
        async def mock_invoke(messages):
            return AIMessage(content="这是一个测试响应")
        
        llm.invoke = mock_invoke
        llm.ainvoke = mock_invoke
        
        return llm
    
    @pytest.mark.asyncio
    async def test_create_react_agent_structure(self, mock_llm, mock_tools):
        """测试 create_react_agent 的基本结构"""
        from langgraph.prebuilt import create_react_agent
        from langgraph.checkpoint.memory import MemorySaver
        
        # 创建检查点存储器
        checkpointer = MemorySaver()
        
        # 使用 create_react_agent 创建代理
        app = create_react_agent(
            model=mock_llm,
            tools=mock_tools,
            checkpointer=checkpointer
        )
        
        # 验证基本属性
        assert app is not None
        assert app.checkpointer is not None
        assert hasattr(app, 'get_graph')
        
        # 验证图结构
        graph = app.get_graph()
        assert 'agent' in graph.nodes
        assert 'tools' in graph.nodes
        
        print("✅ create_react_agent 结构验证通过")
    
    @pytest.mark.asyncio
    async def test_state_compatibility(self):
        """测试状态兼容性"""
        from main import AgentState, EnhancedAgentState
        from langchain_core.messages import HumanMessage, AIMessage
        
        # 测试原有 AgentState
        basic_state = AgentState(messages=[HumanMessage(content="测试")])
        assert "messages" in basic_state
        assert len(basic_state["messages"]) == 1
        
        # 测试增强的 AgentState
        enhanced_state = EnhancedAgentState(
            messages=[HumanMessage(content="测试")],
            user_id="test_user",
            session_id="test_session",
            error_count=0,
            session_metadata={"test": "data"}
        )
        
        assert "messages" in enhanced_state
        assert enhanced_state["user_id"] == "test_user"
        assert enhanced_state["session_id"] == "test_session"
        assert enhanced_state["error_count"] == 0
        assert enhanced_state["session_metadata"]["test"] == "data"
        
        print("✅ 状态兼容性验证通过")
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self):
        """测试错误处理集成"""
        from utils.error_handling import (
            ModelCallError, 
            handle_agent_errors,
            create_user_friendly_error
        )
        
        # 测试错误处理装饰器
        @handle_agent_errors
        async def test_function():
            raise ModelCallError("测试模型错误", "test_model")
        
        result = await test_function()
        assert "messages" in result
        assert len(result["messages"]) == 1
        assert isinstance(result["messages"][0], AIMessage)
        assert "AI模型暂时不可用" in result["messages"][0].content
        
        # 测试用户友好错误消息
        error = ModelCallError("API调用失败", "gpt-4")
        friendly_msg = create_user_friendly_error(error)
        assert "AI模型 gpt-4 暂时不可用" in friendly_msg
        
        print("✅ 错误处理集成验证通过")
    
    @pytest.mark.asyncio
    async def test_state_manager_functionality(self):
        """测试状态管理器功能"""
        from utils.state_manager import StateManager, UserContext, SessionContext
        
        # 创建状态管理器
        manager = StateManager()
        
        # 测试用户上下文创建
        user_ctx = manager.create_user_context(
            user_id="test_user",
            username="测试用户",
            preferences={"language": "zh"}
        )
        
        assert user_ctx.user_id == "test_user"
        assert user_ctx.username == "测试用户"
        assert user_ctx.preferences["language"] == "zh"
        
        # 测试会话上下文创建
        session_ctx = manager.create_session_context(
            user_id="test_user",
            session_id="test_session"
        )
        
        assert session_ctx.session_id == "test_session"
        assert session_ctx.user_id == "test_user"
        assert session_ctx.message_count == 0
        
        # 测试消息记录
        manager.record_message("test_session", tool_used="test_tool")
        
        updated_session = manager.get_session_context("test_session")
        assert updated_session.message_count == 1
        assert "test_tool" in updated_session.tools_used
        
        print("✅ 状态管理器功能验证通过")
    
    @pytest.mark.asyncio
    async def test_metrics_collection(self):
        """测试指标收集功能"""
        from utils.metrics import MetricsCollector
        
        # 创建指标收集器
        collector = MetricsCollector()
        
        # 测试请求记录
        metrics = collector.start_request("test_request", user_id="test_user")
        assert metrics.request_id == "test_request"
        assert metrics.user_id == "test_user"
        
        # 模拟请求完成
        import time
        time.sleep(0.1)  # 模拟处理时间
        
        collector.end_request("test_request", success=True, response_length=100)
        
        # 验证指标
        summary = collector.get_summary()
        assert summary["request_count"] == 1
        assert summary["error_count"] == 0
        assert summary["avg_response_time"] > 0
        
        print("✅ 指标收集功能验证通过")
    
    @pytest.mark.asyncio
    async def test_configuration_system(self):
        """测试配置系统"""
        from config.app_config import AppConfig, LoggingConfig
        
        # 测试配置创建
        config = AppConfig(
            debug_mode=True,
            environment="testing"
        )
        
        assert config.debug_mode is True
        assert config.environment == "testing"
        assert config.is_testing() is True
        
        # 测试日志配置
        log_config = config.get_logging_config()
        assert "log_level" in log_config
        assert "log_file" in log_config
        assert "enable_console" in log_config
        
        print("✅ 配置系统验证通过")
    
    def test_code_simplification_benefits(self):
        """验证代码简化的好处"""
        
        # 统计原有实现的代码行数（从备份文件）
        try:
            with open("main_backup.py", "r", encoding="utf-8") as f:
                old_lines = len(f.readlines())
        except FileNotFoundError:
            old_lines = 0
        
        # 统计新实现的代码行数
        try:
            with open("main.py", "r", encoding="utf-8") as f:
                new_lines = len(f.readlines())
        except FileNotFoundError:
            new_lines = 0
        
        print(f"📊 代码行数对比:")
        print(f"   原有实现: {old_lines} 行")
        print(f"   新实现: {new_lines} 行")
        
        if old_lines > 0:
            reduction = ((old_lines - new_lines) / old_lines) * 100
            print(f"   代码减少: {reduction:.1f}%")
        
        # 验证关键简化点
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 验证不再需要手动定义的函数
        assert "def call_model(" not in content, "❌ 仍然包含手动的 call_model 函数"
        assert "def should_continue(" not in content, "❌ 仍然包含手动的 should_continue 函数"
        assert "StateGraph(" not in content, "❌ 仍然包含手动的 StateGraph 构建"
        
        # 验证使用了 create_react_agent
        assert "create_react_agent" in content, "❌ 未使用 create_react_agent"
        
        print("✅ 代码简化验证通过")
        print("🎉 主要简化成果:")
        print("   - 移除了手动的 call_model 函数定义")
        print("   - 移除了手动的 should_continue 函数定义")
        print("   - 移除了手动的 StateGraph 构建逻辑")
        print("   - 移除了手动的节点和边添加代码")
        print("   - 使用官方预构建代理，包含所有最佳实践")


@pytest.mark.asyncio
async def test_integration_workflow():
    """集成工作流测试"""
    print("\n🔄 开始集成工作流测试...")
    
    try:
        # 测试模块导入
        from main import initialize_agent
        from utils import (
            setup_logging, 
            metrics, 
            state_manager,
            ModelCallError,
            handle_agent_errors
        )
        
        print("✅ 所有模块导入成功")
        
        # 测试日志系统
        setup_logging(log_level="INFO", enable_console=True, log_file=None)
        print("✅ 日志系统初始化成功")
        
        # 测试状态管理器
        user_ctx = state_manager.create_user_context("integration_test_user")
        session_ctx = state_manager.create_session_context("integration_test_user")
        print("✅ 状态管理器工作正常")
        
        # 测试指标收集
        request_metrics = metrics.start_request("integration_test")
        metrics.end_request("integration_test", success=True)
        summary = metrics.get_summary()
        assert summary["request_count"] > 0
        print("✅ 指标收集器工作正常")
        
        print("🎉 集成工作流测试全部通过!")
        return True
        
    except Exception as e:
        print(f"❌ 集成工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 运行集成测试
    success = asyncio.run(test_integration_workflow())
    
    # 运行单元测试
    print("\n🧪 运行单元测试...")
    pytest_result = pytest.main([__file__, "-v", "--tb=short"])
    
    # 综合结果
    overall_success = success and (pytest_result == 0)
    
    if overall_success:
        print("\n🎉 所有迁移测试通过!")
        print("✨ create_react_agent 迁移成功，功能完整性得到验证")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
    
    sys.exit(0 if overall_success else 1)
