"""
性能基准测试模块
用于测试和验证系统性能优化效果
"""

import asyncio
import time
import statistics
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langchain_core.messages import HumanMessage
from utils.metrics import metrics
from utils.logging_config import get_logger, setup_logging_from_config
from config.app_config import app_config

logger = get_logger(__name__)


class PerformanceBenchmark:
    """性能基准测试类"""
    
    def __init__(self):
        self.results = {}
        self.test_messages = [
            "你好，请介绍一下自己",
            "今天天气怎么样？",
            "请帮我写一个Python函数来计算斐波那契数列",
            "什么是机器学习？",
            "请解释一下什么是LangGraph",
            "如何优化Python代码的性能？",
            "请推荐一些学习编程的资源",
            "什么是RESTful API？",
            "请解释一下数据库索引的作用",
            "如何进行代码重构？"
        ]
    
    async def benchmark_agent_response_time(self, app, num_requests: int = 10) -> Dict[str, Any]:
        """基准测试代理响应时间"""
        logger.info(f"开始响应时间基准测试 - {num_requests} 个请求")
        
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        for i in range(num_requests):
            try:
                # 选择测试消息
                message = self.test_messages[i % len(self.test_messages)]
                
                # 创建唯一的线程ID
                thread_id = f"benchmark_{uuid.uuid4().hex[:8]}"
                config = {"configurable": {"thread_id": thread_id}}
                
                start_time = time.time()
                
                # 执行请求
                result = await app.ainvoke(
                    {"messages": [HumanMessage(content=message)]},
                    config=config
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                response_times.append(response_time)
                successful_requests += 1
                
                logger.debug(f"请求 {i+1}/{num_requests} 完成 - 响应时间: {response_time:.3f}s")
                
                # 避免过快的请求
                await asyncio.sleep(0.1)
                
            except Exception as e:
                failed_requests += 1
                logger.error(f"请求 {i+1} 失败: {e}")
        
        if not response_times:
            return {"error": "所有请求都失败了"}
        
        # 计算统计数据
        avg_time = statistics.mean(response_times)
        median_time = statistics.median(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        # 计算百分位数
        sorted_times = sorted(response_times)
        p95_time = sorted_times[int(len(sorted_times) * 0.95)] if len(sorted_times) > 1 else sorted_times[0]
        p99_time = sorted_times[int(len(sorted_times) * 0.99)] if len(sorted_times) > 1 else sorted_times[0]
        
        result = {
            "test_name": "agent_response_time",
            "num_requests": num_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "success_rate": successful_requests / num_requests,
            "response_times": {
                "average": avg_time,
                "median": median_time,
                "min": min_time,
                "max": max_time,
                "p95": p95_time,
                "p99": p99_time,
                "std_dev": statistics.stdev(response_times) if len(response_times) > 1 else 0
            },
            "raw_times": response_times,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"响应时间测试完成 - 平均: {avg_time:.3f}s, P95: {p95_time:.3f}s")
        return result
    
    async def benchmark_concurrent_requests(self, app, concurrent_users: int = 5, 
                                          requests_per_user: int = 3) -> Dict[str, Any]:
        """基准测试并发请求处理能力"""
        logger.info(f"开始并发测试 - {concurrent_users} 个并发用户，每用户 {requests_per_user} 个请求")
        
        async def user_requests(user_id: int):
            """单个用户的请求序列"""
            user_times = []
            user_errors = 0
            
            for req_id in range(requests_per_user):
                try:
                    message = self.test_messages[(user_id * requests_per_user + req_id) % len(self.test_messages)]
                    thread_id = f"concurrent_user_{user_id}_{req_id}"
                    config = {"configurable": {"thread_id": thread_id}}
                    
                    start_time = time.time()
                    result = await app.ainvoke(
                        {"messages": [HumanMessage(content=message)]},
                        config=config
                    )
                    end_time = time.time()
                    
                    user_times.append(end_time - start_time)
                    
                except Exception as e:
                    user_errors += 1
                    logger.error(f"用户 {user_id} 请求 {req_id} 失败: {e}")
            
            return user_times, user_errors
        
        # 并发执行所有用户请求
        start_time = time.time()
        tasks = [user_requests(user_id) for user_id in range(concurrent_users)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # 汇总结果
        all_times = []
        total_errors = 0
        successful_users = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"用户 {i} 完全失败: {result}")
                total_errors += requests_per_user
            else:
                user_times, user_errors = result
                all_times.extend(user_times)
                total_errors += user_errors
                if user_errors < requests_per_user:
                    successful_users += 1
        
        total_requests = concurrent_users * requests_per_user
        successful_requests = len(all_times)
        
        if not all_times:
            return {"error": "所有并发请求都失败了"}
        
        # 计算吞吐量
        throughput = successful_requests / total_time
        
        result = {
            "test_name": "concurrent_requests",
            "concurrent_users": concurrent_users,
            "requests_per_user": requests_per_user,
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": total_errors,
            "successful_users": successful_users,
            "success_rate": successful_requests / total_requests,
            "total_time": total_time,
            "throughput_rps": throughput,
            "response_times": {
                "average": statistics.mean(all_times),
                "median": statistics.median(all_times),
                "min": min(all_times),
                "max": max(all_times),
                "std_dev": statistics.stdev(all_times) if len(all_times) > 1 else 0
            },
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"并发测试完成 - 吞吐量: {throughput:.2f} RPS, 成功率: {result['success_rate']:.2%}")
        return result
    
    async def benchmark_cache_performance(self, app, num_requests: int = 20) -> Dict[str, Any]:
        """基准测试缓存性能"""
        logger.info(f"开始缓存性能测试 - {num_requests} 个请求")
        
        # 使用相同的消息来测试缓存
        test_message = "请解释一下什么是人工智能？"
        
        first_request_times = []
        cached_request_times = []
        
        for i in range(num_requests // 2):
            try:
                # 第一次请求（应该缓存未命中）
                thread_id = f"cache_test_first_{i}"
                config = {"configurable": {"thread_id": thread_id}}
                
                start_time = time.time()
                await app.ainvoke(
                    {"messages": [HumanMessage(content=test_message)]},
                    config=config
                )
                first_time = time.time() - start_time
                first_request_times.append(first_time)
                
                # 第二次请求（应该缓存命中）
                thread_id = f"cache_test_second_{i}"
                config = {"configurable": {"thread_id": thread_id}}
                
                start_time = time.time()
                await app.ainvoke(
                    {"messages": [HumanMessage(content=test_message)]},
                    config=config
                )
                cached_time = time.time() - start_time
                cached_request_times.append(cached_time)
                
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"缓存测试请求 {i} 失败: {e}")
        
        if not first_request_times or not cached_request_times:
            return {"error": "缓存测试失败"}
        
        avg_first = statistics.mean(first_request_times)
        avg_cached = statistics.mean(cached_request_times)
        cache_speedup = avg_first / avg_cached if avg_cached > 0 else 0
        
        result = {
            "test_name": "cache_performance",
            "num_request_pairs": len(first_request_times),
            "first_request_avg": avg_first,
            "cached_request_avg": avg_cached,
            "cache_speedup": cache_speedup,
            "cache_improvement_percent": ((avg_first - avg_cached) / avg_first * 100) if avg_first > 0 else 0,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"缓存测试完成 - 加速比: {cache_speedup:.2f}x")
        return result
    
    def save_results(self, filepath: str):
        """保存测试结果到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            logger.info(f"基准测试结果已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存测试结果失败: {e}")
    
    def print_summary(self):
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print("性能基准测试结果摘要")
        print("="*60)
        
        for test_name, result in self.results.items():
            if "error" in result:
                print(f"\n❌ {test_name}: {result['error']}")
                continue
            
            print(f"\n✅ {test_name}:")
            
            if test_name == "agent_response_time":
                print(f"  • 成功率: {result['success_rate']:.2%}")
                print(f"  • 平均响应时间: {result['response_times']['average']:.3f}s")
                print(f"  • P95响应时间: {result['response_times']['p95']:.3f}s")
                print(f"  • 最快响应: {result['response_times']['min']:.3f}s")
                print(f"  • 最慢响应: {result['response_times']['max']:.3f}s")
            
            elif test_name == "concurrent_requests":
                print(f"  • 并发用户: {result['concurrent_users']}")
                print(f"  • 成功率: {result['success_rate']:.2%}")
                print(f"  • 吞吐量: {result['throughput_rps']:.2f} RPS")
                print(f"  • 平均响应时间: {result['response_times']['average']:.3f}s")
            
            elif test_name == "cache_performance":
                print(f"  • 缓存加速比: {result['cache_speedup']:.2f}x")
                print(f"  • 性能提升: {result['cache_improvement_percent']:.1f}%")
                print(f"  • 首次请求平均时间: {result['first_request_avg']:.3f}s")
                print(f"  • 缓存请求平均时间: {result['cached_request_avg']:.3f}s")


async def run_full_benchmark():
    """运行完整的性能基准测试"""
    # 设置日志
    setup_logging_from_config(app_config)
    
    logger.info("开始性能基准测试")
    
    try:
        # 初始化Agent
        from main import initialize_agent
        app, tools, session_manager = await initialize_agent()
        
        # 创建基准测试实例
        benchmark = PerformanceBenchmark()
        
        # 运行各项测试
        print("🚀 开始性能基准测试...")
        
        # 1. 响应时间测试
        print("\n📊 测试1: 响应时间基准测试")
        benchmark.results["agent_response_time"] = await benchmark.benchmark_agent_response_time(app, 10)
        
        # 2. 并发测试
        print("\n📊 测试2: 并发请求测试")
        benchmark.results["concurrent_requests"] = await benchmark.benchmark_concurrent_requests(app, 3, 2)
        
        # 3. 缓存性能测试
        print("\n📊 测试3: 缓存性能测试")
        benchmark.results["cache_performance"] = await benchmark.benchmark_cache_performance(app, 10)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"tests/benchmark_results_{timestamp}.json"
        benchmark.save_results(results_file)
        
        # 打印摘要
        benchmark.print_summary()
        
        # 获取系统指标
        system_metrics = metrics.get_enhanced_summary()
        print(f"\n📈 系统指标摘要:")
        print(f"  • 总请求数: {system_metrics['request_count']}")
        print(f"  • 错误率: {system_metrics['error_rate']:.2%}")
        print(f"  • 缓存命中率: {system_metrics['cache']['hit_rate']:.2%}")
        
        logger.info("性能基准测试完成")
        
    except Exception as e:
        logger.error(f"基准测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(run_full_benchmark())
