#!/usr/bin/env python3
"""
优化效果验证测试
快速验证阶段三和四的优化是否正常工作
"""

import asyncio
import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.app_config import app_config
from utils.logging_config import setup_logging_from_config, get_logger
from utils.cache import response_cache
from utils.metrics import metrics
from langchain_core.messages import HumanMessage

logger = get_logger(__name__)


async def test_configuration_system():
    """测试配置系统"""
    print("🔧 测试配置系统...")
    
    try:
        # 测试配置加载
        assert app_config is not None, "配置系统未正确加载"
        assert hasattr(app_config, 'environment'), "配置缺少环境设置"
        assert hasattr(app_config, 'logging'), "配置缺少日志设置"
        assert hasattr(app_config, 'performance'), "配置缺少性能设置"
        
        print(f"  ✅ 运行环境: {app_config.environment}")
        print(f"  ✅ 日志级别: {app_config.logging.level}")
        print(f"  ✅ 连接池大小: {app_config.performance.connection_pool_size}")
        print(f"  ✅ 缓存启用: {app_config.performance.enable_caching}")
        
        return True
    except Exception as e:
        print(f"  ❌ 配置系统测试失败: {e}")
        return False


async def test_logging_system():
    """测试日志系统"""
    print("📝 测试日志系统...")
    
    try:
        # 设置日志系统
        setup_logging_from_config(app_config)
        
        # 测试不同级别的日志
        logger.debug("这是一条调试日志")
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告日志")
        
        print("  ✅ 日志系统正常工作")
        return True
    except Exception as e:
        print(f"  ❌ 日志系统测试失败: {e}")
        return False


async def test_cache_system():
    """测试缓存系统"""
    print("💾 测试缓存系统...")
    
    try:
        # 测试缓存基本功能
        test_messages = [HumanMessage(content="测试消息")]
        test_model = "test_model"
        test_response = "测试响应"
        
        # 测试缓存设置和获取
        response_cache.set(test_messages, test_model, test_response)
        cached_result = response_cache.get(test_messages, test_model)
        
        assert cached_result == test_response, "缓存设置/获取失败"
        
        # 测试缓存统计
        stats = response_cache.get_stats()
        assert "enabled" in stats, "缓存统计信息缺失"
        
        print(f"  ✅ 缓存启用状态: {stats['enabled']}")
        print(f"  ✅ 缓存大小: {stats.get('size', 0)}")
        print(f"  ✅ 缓存TTL: {stats.get('ttl', 0)}s")
        
        return True
    except Exception as e:
        print(f"  ❌ 缓存系统测试失败: {e}")
        return False


async def test_metrics_system():
    """测试指标系统"""
    print("📊 测试指标系统...")
    
    try:
        # 测试指标收集
        request_id = "test_request_001"
        
        # 开始请求
        request_metrics = metrics.start_request(request_id, user_id="test_user", model_name="test_model")
        assert request_metrics is not None, "请求指标创建失败"
        
        # 模拟一些处理时间
        await asyncio.sleep(0.1)
        
        # 结束请求
        metrics.end_request(request_id, success=True, response_length=100)
        
        # 获取指标摘要
        summary = metrics.get_enhanced_summary()
        assert "request_count" in summary, "指标摘要缺失基本信息"
        assert summary["request_count"] > 0, "请求计数未更新"
        
        print(f"  ✅ 总请求数: {summary['request_count']}")
        print(f"  ✅ 错误率: {summary['error_rate']:.2%}")
        print(f"  ✅ 平均响应时间: {summary['avg_response_time']:.3f}s")
        print(f"  ✅ 活跃用户数: {summary['active_users']}")
        
        return True
    except Exception as e:
        print(f"  ❌ 指标系统测试失败: {e}")
        return False


async def test_agent_initialization():
    """测试Agent初始化"""
    print("🤖 测试Agent初始化...")
    
    try:
        from main import initialize_agent
        
        start_time = time.time()
        app, tools, session_manager = await initialize_agent()
        init_time = time.time() - start_time
        
        assert app is not None, "Agent应用未正确初始化"
        assert tools is not None, "工具未正确加载"
        assert session_manager is not None, "会话管理器未正确创建"
        
        print(f"  ✅ Agent初始化成功 - 耗时: {init_time:.3f}s")
        print(f"  ✅ 工具数量: {len(tools)}")
        print(f"  ✅ 会话管理器类型: {type(session_manager).__name__}")
        
        return True, (app, tools, session_manager)
    except Exception as e:
        print(f"  ❌ Agent初始化测试失败: {e}")
        return False, None


async def test_agent_response():
    """测试Agent响应"""
    print("💬 测试Agent响应...")
    
    try:
        from main import initialize_agent
        
        app, tools, session_manager = await initialize_agent()
        
        # 测试简单对话
        test_message = "你好，请简单介绍一下自己"
        thread_id = "test_thread_001"
        config = {"configurable": {"thread_id": thread_id}}
        
        start_time = time.time()
        result = await app.ainvoke(
            {"messages": [HumanMessage(content=test_message)]},
            config=config
        )
        response_time = time.time() - start_time
        
        assert result is not None, "Agent响应为空"
        assert "messages" in result, "响应格式错误"
        assert len(result["messages"]) > 0, "响应消息为空"
        
        print(f"  ✅ 响应时间: {response_time:.3f}s")
        print(f"  ✅ 响应消息数: {len(result['messages'])}")
        print(f"  ✅ 最后消息长度: {len(result['messages'][-1].content)}字符")
        
        return True
    except Exception as e:
        print(f"  ❌ Agent响应测试失败: {e}")
        return False


async def test_performance_improvements():
    """测试性能改进"""
    print("⚡ 测试性能改进...")
    
    try:
        from main import initialize_agent
        
        app, tools, session_manager = await initialize_agent()
        
        # 测试缓存效果
        test_message = "什么是人工智能？"
        
        # 第一次请求（缓存未命中）
        start_time = time.time()
        result1 = await app.ainvoke(
            {"messages": [HumanMessage(content=test_message)]},
            config={"configurable": {"thread_id": "perf_test_1"}}
        )
        first_time = time.time() - start_time
        
        # 第二次请求（可能缓存命中）
        start_time = time.time()
        result2 = await app.ainvoke(
            {"messages": [HumanMessage(content=test_message)]},
            config={"configurable": {"thread_id": "perf_test_2"}}
        )
        second_time = time.time() - start_time
        
        # 计算性能改进
        if second_time < first_time:
            improvement = ((first_time - second_time) / first_time) * 100
            print(f"  ✅ 第一次请求: {first_time:.3f}s")
            print(f"  ✅ 第二次请求: {second_time:.3f}s")
            print(f"  ✅ 性能改进: {improvement:.1f}%")
        else:
            print(f"  ⚠️ 第一次请求: {first_time:.3f}s")
            print(f"  ⚠️ 第二次请求: {second_time:.3f}s")
            print(f"  ⚠️ 未检测到明显的性能改进")
        
        return True
    except Exception as e:
        print(f"  ❌ 性能改进测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始优化效果验证测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(await test_configuration_system())
    test_results.append(await test_logging_system())
    test_results.append(await test_cache_system())
    test_results.append(await test_metrics_system())
    
    # Agent相关测试
    init_success, agent_components = await test_agent_initialization()
    test_results.append(init_success)
    
    if init_success:
        test_results.append(await test_agent_response())
        test_results.append(await test_performance_improvements())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print("=" * 50)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"✅ 通过测试: {passed_tests}/{total_tests}")
    print(f"📊 成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 优化效果验证成功！系统运行良好。")
    elif success_rate >= 60:
        print("⚠️ 优化效果部分成功，建议检查失败的测试项。")
    else:
        print("❌ 优化效果验证失败，需要进一步调试。")
    
    # 显示最终的系统指标
    if passed_tests > 0:
        print("\n📈 最终系统指标:")
        final_metrics = metrics.get_enhanced_summary()
        print(f"  • 总请求数: {final_metrics['request_count']}")
        print(f"  • 错误率: {final_metrics['error_rate']:.2%}")
        print(f"  • 平均响应时间: {final_metrics['avg_response_time']:.3f}s")
        print(f"  • 缓存命中率: {final_metrics['cache']['hit_rate']:.2%}")
        print(f"  • 系统健康状态: {final_metrics['health_status']['status']}")


if __name__ == "__main__":
    asyncio.run(main())
