"""
缓存系统模块
提供响应缓存、LRU缓存和基于时间的缓存功能
"""

import hashlib
import json
import time
from typing import Any, Dict, Optional, Tuple
from collections import OrderedDict
import threading
from utils.logging_config import get_logger

# 导入配置系统
try:
    from config.app_config import app_config
except ImportError:
    app_config = None

logger = get_logger(__name__)


class LRUCache:
    """LRU (Least Recently Used) 缓存实现"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: OrderedDict = OrderedDict()
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key in self.cache:
                # 移动到末尾（最近使用）
                value = self.cache.pop(key)
                self.cache[key] = value
                return value
            return None
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存值"""
        with self.lock:
            if key in self.cache:
                # 更新现有值
                self.cache.pop(key)
            elif len(self.cache) >= self.max_size:
                # 移除最久未使用的项
                oldest_key = next(iter(self.cache))
                self.cache.pop(oldest_key)
                logger.debug(f"缓存已满，移除最久未使用的项: {oldest_key}")
            
            self.cache[key] = value
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            logger.info("缓存已清空")
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def keys(self) -> list:
        """获取所有缓存键"""
        with self.lock:
            return list(self.cache.keys())


class TTLCache:
    """带过期时间的缓存 (Time To Live)"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, Tuple[Any, float]] = {}  # (value, expire_time)
        self.lock = threading.RLock()
    
    def _is_expired(self, expire_time: float) -> bool:
        """检查是否过期"""
        return time.time() > expire_time
    
    def _cleanup_expired(self) -> None:
        """清理过期项"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, expire_time) in self.cache.items()
            if current_time > expire_time
        ]
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key in self.cache:
                value, expire_time = self.cache[key]
                if not self._is_expired(expire_time):
                    return value
                else:
                    # 删除过期项
                    del self.cache[key]
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        with self.lock:
            # 清理过期项
            self._cleanup_expired()
            
            # 如果缓存已满，移除一些项
            if len(self.cache) >= self.max_size:
                # 移除最早过期的项
                oldest_key = min(self.cache.keys(), 
                               key=lambda k: self.cache[k][1])
                del self.cache[oldest_key]
                logger.debug(f"缓存已满，移除最早过期的项: {oldest_key}")
            
            # 设置过期时间
            ttl = ttl or self.default_ttl
            expire_time = time.time() + ttl
            self.cache[key] = (value, expire_time)
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            logger.info("TTL缓存已清空")
    
    def size(self) -> int:
        """获取缓存大小"""
        with self.lock:
            self._cleanup_expired()
            return len(self.cache)


class ResponseCache:
    """响应缓存系统，专门用于缓存模型响应"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.enabled = app_config.performance.enable_caching if app_config else True
        self.ttl = app_config.performance.cache_ttl if app_config else ttl
        
        if self.enabled:
            self.cache = TTLCache(max_size=max_size, default_ttl=self.ttl)
            logger.info(f"响应缓存已启用 - 最大大小: {max_size}, TTL: {self.ttl}s")
        else:
            self.cache = None
            logger.info("响应缓存已禁用")
    
    def _generate_cache_key(self, messages: list, model: str, **kwargs) -> str:
        """生成缓存键"""
        # 提取消息内容
        message_contents = []
        for msg in messages:
            if hasattr(msg, 'content'):
                message_contents.append(msg.content)
            else:
                message_contents.append(str(msg))
        
        # 创建缓存键的数据
        cache_data = {
            "messages": message_contents,
            "model": model,
            "kwargs": sorted(kwargs.items())
        }
        
        # 生成哈希
        cache_str = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(cache_str.encode('utf-8')).hexdigest()
    
    def get(self, messages: list, model: str, **kwargs) -> Optional[Any]:
        """获取缓存的响应"""
        if not self.enabled or not self.cache:
            return None
        
        cache_key = self._generate_cache_key(messages, model, **kwargs)
        result = self.cache.get(cache_key)
        
        if result:
            logger.debug(f"缓存命中: {cache_key[:8]}...")
        
        return result
    
    def set(self, messages: list, model: str, response: Any, **kwargs) -> None:
        """缓存响应"""
        if not self.enabled or not self.cache:
            return
        
        cache_key = self._generate_cache_key(messages, model, **kwargs)
        self.cache.set(cache_key, response)
        logger.debug(f"响应已缓存: {cache_key[:8]}...")
    
    def clear(self) -> None:
        """清空缓存"""
        if self.cache:
            self.cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        if not self.cache:
            return {"enabled": False}
        
        return {
            "enabled": self.enabled,
            "size": self.cache.size(),
            "max_size": self.cache.max_size,
            "ttl": self.ttl
        }


# 全局缓存实例
response_cache = ResponseCache()


def cache_response(func):
    """响应缓存装饰器"""
    def wrapper(*args, **kwargs):
        # 尝试从缓存获取
        if len(args) >= 2:  # 假设第一个参数是 self，第二个是 messages
            messages = args[1]
            model = getattr(args[0], 'model', 'unknown')
            
            cached_result = response_cache.get(messages, model, **kwargs)
            if cached_result is not None:
                return cached_result
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 缓存结果
        if len(args) >= 2:
            response_cache.set(messages, model, result, **kwargs)
        
        return result
    
    return wrapper


async def cache_response_async(func):
    """异步响应缓存装饰器"""
    async def wrapper(*args, **kwargs):
        # 尝试从缓存获取
        if len(args) >= 2:
            messages = args[1]
            model = getattr(args[0], 'model', 'unknown')
            
            cached_result = response_cache.get(messages, model, **kwargs)
            if cached_result is not None:
                return cached_result
        
        # 执行函数
        result = await func(*args, **kwargs)
        
        # 缓存结果
        if len(args) >= 2:
            response_cache.set(messages, model, result, **kwargs)
        
        return result
    
    return wrapper
