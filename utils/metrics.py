"""
性能指标收集模块
提供应用性能监控和指标收集功能
"""

import time
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import json
import logging

logger = logging.getLogger(__name__)


@dataclass
class RequestMetrics:
    """单次请求的指标数据"""
    request_id: str
    start_time: float
    end_time: Optional[float] = None
    success: bool = True
    error_type: Optional[str] = None
    user_id: Optional[str] = None
    model_name: Optional[str] = None
    tool_calls: List[str] = field(default_factory=list)
    response_length: int = 0
    
    @property
    def duration(self) -> float:
        """请求持续时间(秒)"""
        if self.end_time is None:
            return time.time() - self.start_time
        return self.end_time - self.start_time


class MetricsCollector:
    """应用指标收集器"""
    
    def __init__(self, max_history: int = 10000):
        self.max_history = max_history
        self.lock = threading.Lock()
        
        # 基础指标
        self.request_count = 0
        self.error_count = 0
        self.total_response_time = 0.0
        
        # 详细指标
        self.response_times = deque(maxlen=max_history)
        self.error_types = defaultdict(int)
        self.tool_usage = defaultdict(int)
        self.model_usage = defaultdict(int)
        self.user_activity = defaultdict(int)
        
        # 时间窗口指标 (最近1小时)
        self.hourly_requests = deque(maxlen=3600)  # 每秒一个数据点
        self.hourly_errors = deque(maxlen=3600)
        
        # 活跃请求
        self.active_requests: Dict[str, RequestMetrics] = {}
        
        # 启动时间
        self.start_time = time.time()
    
    def start_request(self, request_id: str, user_id: Optional[str] = None, 
                     model_name: Optional[str] = None) -> RequestMetrics:
        """开始记录请求"""
        with self.lock:
            metrics = RequestMetrics(
                request_id=request_id,
                start_time=time.time(),
                user_id=user_id,
                model_name=model_name
            )
            self.active_requests[request_id] = metrics
            return metrics
    
    def end_request(self, request_id: str, success: bool = True, 
                   error_type: Optional[str] = None, response_length: int = 0):
        """结束请求记录"""
        with self.lock:
            if request_id not in self.active_requests:
                logger.warning(f"未找到请求ID: {request_id}")
                return
            
            metrics = self.active_requests.pop(request_id)
            metrics.end_time = time.time()
            metrics.success = success
            metrics.error_type = error_type
            metrics.response_length = response_length
            
            # 更新统计数据
            self.request_count += 1
            self.total_response_time += metrics.duration
            self.response_times.append(metrics.duration)
            
            if not success:
                self.error_count += 1
                if error_type:
                    self.error_types[error_type] += 1
            
            if metrics.model_name:
                self.model_usage[metrics.model_name] += 1
            
            if metrics.user_id:
                self.user_activity[metrics.user_id] += 1
            
            for tool in metrics.tool_calls:
                self.tool_usage[tool] += 1
            
            # 更新时间窗口指标
            current_time = int(time.time())
            self.hourly_requests.append((current_time, 1))
            if not success:
                self.hourly_errors.append((current_time, 1))
            
            # 清理过期的时间窗口数据
            self._cleanup_hourly_data()
    
    def record_tool_usage(self, request_id: str, tool_name: str):
        """记录工具使用"""
        with self.lock:
            if request_id in self.active_requests:
                self.active_requests[request_id].tool_calls.append(tool_name)
    
    def _cleanup_hourly_data(self):
        """清理过期的小时数据"""
        cutoff_time = int(time.time()) - 3600  # 1小时前
        
        while self.hourly_requests and self.hourly_requests[0][0] < cutoff_time:
            self.hourly_requests.popleft()
        
        while self.hourly_errors and self.hourly_errors[0][0] < cutoff_time:
            self.hourly_errors.popleft()
    
    def get_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        with self.lock:
            if not self.response_times:
                avg_response_time = 0
                p95_response_time = 0
                p99_response_time = 0
            else:
                sorted_times = sorted(self.response_times)
                avg_response_time = sum(sorted_times) / len(sorted_times)
                p95_index = int(len(sorted_times) * 0.95)
                p99_index = int(len(sorted_times) * 0.99)
                p95_response_time = sorted_times[p95_index] if p95_index < len(sorted_times) else sorted_times[-1]
                p99_response_time = sorted_times[p99_index] if p99_index < len(sorted_times) else sorted_times[-1]
            
            error_rate = (self.error_count / self.request_count) if self.request_count > 0 else 0
            uptime = time.time() - self.start_time
            
            # 计算每小时请求数
            hourly_request_count = len(self.hourly_requests)
            hourly_error_count = len(self.hourly_errors)
            
            return {
                # 基础指标
                "request_count": self.request_count,
                "error_count": self.error_count,
                "error_rate": error_rate,
                "uptime_seconds": uptime,
                
                # 响应时间指标
                "avg_response_time": avg_response_time,
                "p95_response_time": p95_response_time,
                "p99_response_time": p99_response_time,
                
                # 时间窗口指标
                "hourly_requests": hourly_request_count,
                "hourly_errors": hourly_error_count,
                "hourly_error_rate": (hourly_error_count / hourly_request_count) if hourly_request_count > 0 else 0,
                
                # 使用统计
                "top_error_types": dict(sorted(self.error_types.items(), key=lambda x: x[1], reverse=True)[:5]),
                "top_tools": dict(sorted(self.tool_usage.items(), key=lambda x: x[1], reverse=True)[:10]),
                "model_usage": dict(self.model_usage),
                "active_users": len(self.user_activity),
                "active_requests": len(self.active_requests),
                
                # 时间戳
                "timestamp": datetime.now().isoformat()
            }
    
    def get_detailed_metrics(self) -> Dict[str, Any]:
        """获取详细指标"""
        summary = self.get_summary()
        
        with self.lock:
            summary.update({
                "all_error_types": dict(self.error_types),
                "all_tools": dict(self.tool_usage),
                "user_activity": dict(self.user_activity),
                "recent_response_times": list(self.response_times)[-100:],  # 最近100个响应时间
            })
        
        return summary
    
    def reset_metrics(self):
        """重置所有指标"""
        with self.lock:
            self.request_count = 0
            self.error_count = 0
            self.total_response_time = 0.0
            self.response_times.clear()
            self.error_types.clear()
            self.tool_usage.clear()
            self.model_usage.clear()
            self.user_activity.clear()
            self.hourly_requests.clear()
            self.hourly_errors.clear()
            self.active_requests.clear()
            self.start_time = time.time()
    
    def export_metrics(self, filepath: str):
        """导出指标到文件"""
        metrics = self.get_detailed_metrics()
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, indent=2, ensure_ascii=False)
            logger.info(f"指标已导出到: {filepath}")
        except Exception as e:
            logger.error(f"导出指标失败: {e}")


class PerformanceMonitor:
    """性能监控装饰器"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
    
    def monitor(self, operation_name: str = None):
        """性能监控装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                import uuid
                request_id = str(uuid.uuid4())
                op_name = operation_name or f"{func.__module__}.{func.__name__}"
                
                # 开始监控
                self.metrics.start_request(request_id)
                
                try:
                    result = func(*args, **kwargs)
                    self.metrics.end_request(request_id, success=True)
                    return result
                except Exception as e:
                    self.metrics.end_request(
                        request_id, 
                        success=False, 
                        error_type=type(e).__name__
                    )
                    raise
            
            return wrapper
        return decorator


class SystemMetrics:
    """系统资源监控"""

    def __init__(self):
        self.lock = threading.Lock()
        self._last_cpu_times = None
        self._last_check_time = None

    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()

            return {
                "rss": memory_info.rss,  # 物理内存
                "vms": memory_info.vms,  # 虚拟内存
                "percent": process.memory_percent(),
                "available": psutil.virtual_memory().available,
                "total": psutil.virtual_memory().total
            }
        except ImportError:
            return {"error": "psutil not available"}
        except Exception as e:
            return {"error": str(e)}

    def get_cpu_usage(self) -> Dict[str, Any]:
        """获取CPU使用情况"""
        try:
            import psutil
            process = psutil.Process()

            with self.lock:
                current_time = time.time()
                cpu_percent = process.cpu_percent()

                # 计算CPU使用率变化
                if self._last_check_time:
                    time_delta = current_time - self._last_check_time
                else:
                    time_delta = 0

                self._last_check_time = current_time

                return {
                    "process_cpu_percent": cpu_percent,
                    "system_cpu_percent": psutil.cpu_percent(),
                    "cpu_count": psutil.cpu_count(),
                    "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
                    "time_delta": time_delta
                }
        except ImportError:
            return {"error": "psutil not available"}
        except Exception as e:
            return {"error": str(e)}

    def get_disk_usage(self) -> Dict[str, Any]:
        """获取磁盘使用情况"""
        try:
            import psutil
            disk_usage = psutil.disk_usage('.')

            return {
                "total": disk_usage.total,
                "used": disk_usage.used,
                "free": disk_usage.free,
                "percent": (disk_usage.used / disk_usage.total) * 100
            }
        except ImportError:
            return {"error": "psutil not available"}
        except Exception as e:
            return {"error": str(e)}


class CacheMetrics:
    """缓存指标监控"""

    def __init__(self):
        self.lock = threading.Lock()
        self.cache_hits = 0
        self.cache_misses = 0
        self.cache_sets = 0
        self.cache_deletes = 0
        self.hit_times = deque(maxlen=1000)
        self.miss_times = deque(maxlen=1000)

    def record_hit(self, response_time: float = 0):
        """记录缓存命中"""
        with self.lock:
            self.cache_hits += 1
            self.hit_times.append(time.time())

    def record_miss(self, response_time: float = 0):
        """记录缓存未命中"""
        with self.lock:
            self.cache_misses += 1
            self.miss_times.append(time.time())

    def record_set(self):
        """记录缓存设置"""
        with self.lock:
            self.cache_sets += 1

    def record_delete(self):
        """记录缓存删除"""
        with self.lock:
            self.cache_deletes += 1

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            total_requests = self.cache_hits + self.cache_misses
            hit_rate = (self.cache_hits / total_requests) if total_requests > 0 else 0

            # 计算最近1小时的命中率
            current_time = time.time()
            hour_ago = current_time - 3600

            recent_hits = sum(1 for t in self.hit_times if t > hour_ago)
            recent_misses = sum(1 for t in self.miss_times if t > hour_ago)
            recent_total = recent_hits + recent_misses
            recent_hit_rate = (recent_hits / recent_total) if recent_total > 0 else 0

            return {
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "cache_sets": self.cache_sets,
                "cache_deletes": self.cache_deletes,
                "hit_rate": hit_rate,
                "recent_hit_rate": recent_hit_rate,
                "recent_requests": recent_total
            }


class EnhancedMetricsCollector(MetricsCollector):
    """增强的指标收集器"""

    def __init__(self, max_history: int = 10000):
        super().__init__(max_history)
        self.system_metrics = SystemMetrics()
        self.cache_metrics = CacheMetrics()

        # 额外的指标
        self.session_count = 0
        self.concurrent_users = set()
        self.peak_concurrent_users = 0

        # 性能阈值告警
        self.response_time_threshold = 5.0  # 5秒
        self.error_rate_threshold = 0.1     # 10%
        self.slow_requests = deque(maxlen=100)

        # 导入配置系统
        try:
            from config.app_config import app_config
            if app_config:
                self.response_time_threshold = app_config.performance.request_timeout * 0.8
        except ImportError:
            pass

    def start_request(self, request_id: str, user_id: Optional[str] = None,
                     model_name: Optional[str] = None) -> RequestMetrics:
        """开始记录请求（增强版）"""
        metrics = super().start_request(request_id, user_id, model_name)

        # 跟踪并发用户
        if user_id:
            with self.lock:
                self.concurrent_users.add(user_id)
                if len(self.concurrent_users) > self.peak_concurrent_users:
                    self.peak_concurrent_users = len(self.concurrent_users)

        return metrics

    def end_request(self, request_id: str, success: bool = True,
                   error_type: Optional[str] = None, response_length: int = 0):
        """结束请求记录（增强版）"""
        super().end_request(request_id, success, error_type, response_length)

        # 检查慢请求
        if request_id in self.active_requests:
            metrics = self.active_requests[request_id]
            if metrics.duration > self.response_time_threshold:
                with self.lock:
                    self.slow_requests.append({
                        "request_id": request_id,
                        "duration": metrics.duration,
                        "timestamp": datetime.now().isoformat(),
                        "user_id": metrics.user_id,
                        "model_name": metrics.model_name
                    })

    def record_cache_hit(self):
        """记录缓存命中"""
        self.cache_metrics.record_hit()

    def record_cache_miss(self):
        """记录缓存未命中"""
        self.cache_metrics.record_miss()

    def start_session(self, user_id: str):
        """开始新会话"""
        with self.lock:
            self.session_count += 1
            self.concurrent_users.add(user_id)

    def end_session(self, user_id: str):
        """结束会话"""
        with self.lock:
            self.concurrent_users.discard(user_id)

    def get_enhanced_summary(self) -> Dict[str, Any]:
        """获取增强的指标摘要"""
        summary = self.get_summary()

        # 添加系统指标
        summary.update({
            "system": self.system_metrics.get_memory_usage(),
            "cpu": self.system_metrics.get_cpu_usage(),
            "disk": self.system_metrics.get_disk_usage(),
            "cache": self.cache_metrics.get_stats(),

            # 会话指标
            "session_count": self.session_count,
            "concurrent_users": len(self.concurrent_users),
            "peak_concurrent_users": self.peak_concurrent_users,

            # 性能告警
            "slow_requests_count": len(self.slow_requests),
            "recent_slow_requests": list(self.slow_requests)[-10:],

            # 健康状态
            "health_status": self._get_health_status()
        })

        return summary

    def _get_health_status(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        with self.lock:
            # 计算错误率
            error_rate = (self.error_count / self.request_count) if self.request_count > 0 else 0

            # 计算平均响应时间
            avg_response_time = (sum(self.response_times) / len(self.response_times)) if self.response_times else 0

            # 判断健康状态
            status = "healthy"
            issues = []

            if error_rate > self.error_rate_threshold:
                status = "warning"
                issues.append(f"高错误率: {error_rate:.2%}")

            if avg_response_time > self.response_time_threshold:
                status = "warning"
                issues.append(f"响应时间过长: {avg_response_time:.2f}s")

            if len(self.active_requests) > 100:  # 假设100为并发请求上限
                status = "critical"
                issues.append(f"并发请求过多: {len(self.active_requests)}")

            return {
                "status": status,
                "issues": issues,
                "error_rate": error_rate,
                "avg_response_time": avg_response_time,
                "active_requests": len(self.active_requests)
            }


# 全局指标收集器实例（使用增强版）
metrics = EnhancedMetricsCollector()

# 全局性能监控器
performance_monitor = PerformanceMonitor(metrics)
