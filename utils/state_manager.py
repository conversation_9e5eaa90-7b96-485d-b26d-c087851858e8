"""
状态管理工具
提供增强的状态管理功能，包括用户上下文、会话管理和性能跟踪
"""

import time
import uuid
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
import logging

from utils.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class UserContext:
    """用户上下文信息"""
    user_id: str
    username: Optional[str] = None
    email: Optional[str] = None
    preferences: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    last_active: datetime = field(default_factory=datetime.now)
    session_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email,
            "preferences": self.preferences,
            "created_at": self.created_at.isoformat(),
            "last_active": self.last_active.isoformat(),
            "session_count": self.session_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "UserContext":
        """从字典创建"""
        return cls(
            user_id=data["user_id"],
            username=data.get("username"),
            email=data.get("email"),
            preferences=data.get("preferences", {}),
            created_at=datetime.fromisoformat(data.get("created_at", datetime.now().isoformat())),
            last_active=datetime.fromisoformat(data.get("last_active", datetime.now().isoformat())),
            session_count=data.get("session_count", 0)
        )


@dataclass
class SessionContext:
    """会话上下文信息"""
    session_id: str
    user_id: str
    created_at: datetime = field(default_factory=datetime.now)
    last_active: datetime = field(default_factory=datetime.now)
    message_count: int = 0
    error_count: int = 0
    tools_used: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "created_at": self.created_at.isoformat(),
            "last_active": self.last_active.isoformat(),
            "message_count": self.message_count,
            "error_count": self.error_count,
            "tools_used": self.tools_used,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SessionContext":
        """从字典创建"""
        return cls(
            session_id=data["session_id"],
            user_id=data["user_id"],
            created_at=datetime.fromisoformat(data.get("created_at", datetime.now().isoformat())),
            last_active=datetime.fromisoformat(data.get("last_active", datetime.now().isoformat())),
            message_count=data.get("message_count", 0),
            error_count=data.get("error_count", 0),
            tools_used=data.get("tools_used", []),
            metadata=data.get("metadata", {})
        )


class StateManager:
    """状态管理器"""
    
    def __init__(self):
        self.user_contexts: Dict[str, UserContext] = {}
        self.session_contexts: Dict[str, SessionContext] = {}
        self.active_sessions: Dict[str, str] = {}  # session_id -> user_id
    
    def create_user_context(self, user_id: str, username: Optional[str] = None, 
                          email: Optional[str] = None, preferences: Optional[Dict] = None) -> UserContext:
        """创建用户上下文"""
        if user_id in self.user_contexts:
            logger.info(f"用户上下文已存在: {user_id}")
            return self.user_contexts[user_id]
        
        user_context = UserContext(
            user_id=user_id,
            username=username,
            email=email,
            preferences=preferences or {}
        )
        
        self.user_contexts[user_id] = user_context
        logger.info(f"创建用户上下文: {user_id}")
        return user_context
    
    def get_user_context(self, user_id: str) -> Optional[UserContext]:
        """获取用户上下文"""
        return self.user_contexts.get(user_id)
    
    def update_user_context(self, user_id: str, **kwargs) -> Optional[UserContext]:
        """更新用户上下文"""
        if user_id not in self.user_contexts:
            return None
        
        user_context = self.user_contexts[user_id]
        for key, value in kwargs.items():
            if hasattr(user_context, key):
                setattr(user_context, key, value)
        
        user_context.last_active = datetime.now()
        logger.debug(f"更新用户上下文: {user_id}")
        return user_context
    
    def create_session_context(self, user_id: str, session_id: Optional[str] = None) -> SessionContext:
        """创建会话上下文"""
        if session_id is None:
            session_id = str(uuid.uuid4())
        
        if session_id in self.session_contexts:
            logger.info(f"会话上下文已存在: {session_id}")
            return self.session_contexts[session_id]
        
        session_context = SessionContext(
            session_id=session_id,
            user_id=user_id
        )
        
        self.session_contexts[session_id] = session_context
        self.active_sessions[session_id] = user_id
        
        # 更新用户会话计数
        if user_id in self.user_contexts:
            self.user_contexts[user_id].session_count += 1
        
        logger.info(f"创建会话上下文: {session_id} (用户: {user_id})")
        return session_context
    
    def get_session_context(self, session_id: str) -> Optional[SessionContext]:
        """获取会话上下文"""
        return self.session_contexts.get(session_id)
    
    def update_session_context(self, session_id: str, **kwargs) -> Optional[SessionContext]:
        """更新会话上下文"""
        if session_id not in self.session_contexts:
            return None
        
        session_context = self.session_contexts[session_id]
        for key, value in kwargs.items():
            if hasattr(session_context, key):
                setattr(session_context, key, value)
        
        session_context.last_active = datetime.now()
        logger.debug(f"更新会话上下文: {session_id}")
        return session_context
    
    def record_message(self, session_id: str, tool_used: Optional[str] = None, 
                      error_occurred: bool = False):
        """记录消息活动"""
        session_context = self.get_session_context(session_id)
        if session_context:
            session_context.message_count += 1
            session_context.last_active = datetime.now()
            
            if tool_used and tool_used not in session_context.tools_used:
                session_context.tools_used.append(tool_used)
            
            if error_occurred:
                session_context.error_count += 1
            
            # 更新用户最后活跃时间
            user_context = self.get_user_context(session_context.user_id)
            if user_context:
                user_context.last_active = datetime.now()
    
    def close_session(self, session_id: str):
        """关闭会话"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            logger.info(f"关闭会话: {session_id}")
    
    def cleanup_inactive_sessions(self, timeout_hours: int = 24):
        """清理不活跃的会话"""
        cutoff_time = datetime.now() - timedelta(hours=timeout_hours)
        inactive_sessions = []
        
        for session_id, session_context in self.session_contexts.items():
            if session_context.last_active < cutoff_time:
                inactive_sessions.append(session_id)
        
        for session_id in inactive_sessions:
            self.close_session(session_id)
            if session_id in self.session_contexts:
                del self.session_contexts[session_id]
        
        if inactive_sessions:
            logger.info(f"清理了 {len(inactive_sessions)} 个不活跃会话")
    
    def get_user_sessions(self, user_id: str) -> List[SessionContext]:
        """获取用户的所有会话"""
        return [
            session for session in self.session_contexts.values()
            if session.user_id == user_id
        ]
    
    def get_active_sessions_count(self) -> int:
        """获取活跃会话数量"""
        return len(self.active_sessions)
    
    def get_total_users_count(self) -> int:
        """获取总用户数量"""
        return len(self.user_contexts)
    
    def export_state(self, filepath: str):
        """导出状态到文件"""
        state_data = {
            "users": {uid: ctx.to_dict() for uid, ctx in self.user_contexts.items()},
            "sessions": {sid: ctx.to_dict() for sid, ctx in self.session_contexts.items()},
            "active_sessions": self.active_sessions,
            "exported_at": datetime.now().isoformat()
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
            logger.info(f"状态已导出到: {filepath}")
        except Exception as e:
            logger.error(f"导出状态失败: {e}")
    
    def import_state(self, filepath: str):
        """从文件导入状态"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 导入用户上下文
            for uid, data in state_data.get("users", {}).items():
                self.user_contexts[uid] = UserContext.from_dict(data)
            
            # 导入会话上下文
            for sid, data in state_data.get("sessions", {}).items():
                self.session_contexts[sid] = SessionContext.from_dict(data)
            
            # 导入活跃会话
            self.active_sessions = state_data.get("active_sessions", {})
            
            logger.info(f"状态已从 {filepath} 导入")
            
        except Exception as e:
            logger.error(f"导入状态失败: {e}")


# 全局状态管理器实例
state_manager = StateManager()
