"""
日志配置模块
提供统一的日志配置、格式化和轮转功能
"""

import logging
import logging.handlers
import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

# 导入配置系统
try:
    from config.app_config import AppConfig
except ImportError:
    AppConfig = None


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logging_from_config(config: 'AppConfig') -> logging.Logger:
    """
    从配置对象设置日志系统

    Args:
        config: 应用配置对象

    Returns:
        配置好的根日志器
    """
    logging_config = config.get_logging_config()
    return setup_logging(**logging_config)


def setup_logging_from_config(config: 'AppConfig') -> logging.Logger:
    """
    从配置对象设置日志系统

    Args:
        config: 应用配置对象

    Returns:
        配置好的根日志器
    """
    logging_config = config.get_logging_config()
    return setup_logging(**logging_config)


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    log_dir: str = "logs",
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    enable_console: bool = True,
    enable_colors: bool = True,
    format_string: Optional[str] = None
) -> logging.Logger:
    """
    设置统一的日志系统
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件名，如果为None则不写入文件
        log_dir: 日志目录
        max_file_size: 单个日志文件最大大小(字节)
        backup_count: 保留的日志文件数量
        enable_console: 是否启用控制台输出
        enable_colors: 是否启用彩色输出
        format_string: 自定义日志格式
        
    Returns:
        配置好的根日志器
    """
    
    # 创建日志目录
    if log_file:
        log_path = Path(log_dir)
        log_path.mkdir(exist_ok=True)
    
    # 默认日志格式
    if format_string is None:
        format_string = (
            '%(asctime)s - %(name)s - %(levelname)s - '
            '%(filename)s:%(lineno)d - %(funcName)s() - %(message)s'
        )
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有的处理器
    root_logger.handlers.clear()
    
    handlers = []
    
    # 控制台处理器
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        if enable_colors and sys.stdout.isatty():
            console_formatter = ColoredFormatter(format_string)
        else:
            console_formatter = logging.Formatter(format_string)
        console_handler.setFormatter(console_formatter)
        handlers.append(console_handler)
    
    # 文件处理器（带轮转）
    if log_file:
        file_path = Path(log_dir) / log_file
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_formatter = logging.Formatter(format_string)
        file_handler.setFormatter(file_formatter)
        handlers.append(file_handler)
    
    # 添加处理器到根日志器
    for handler in handlers:
        root_logger.addHandler(handler)
    
    # 设置第三方库的日志级别
    _configure_third_party_loggers()
    
    # 记录日志系统启动信息
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统已启动 - 级别: {log_level}, 文件: {log_file or '无'}")
    
    return root_logger


def _configure_third_party_loggers():
    """配置第三方库的日志级别"""
    third_party_loggers = {
        'httpx': logging.WARNING,
        'httpcore': logging.WARNING,
        'urllib3': logging.WARNING,
        'requests': logging.WARNING,
        'aiohttp': logging.WARNING,
        'asyncio': logging.WARNING,
        'langchain': logging.INFO,
        'langgraph': logging.INFO,
        'chainlit': logging.INFO,
        'aiosqlite': logging.WARNING,  # 减少SQLite debug信息
        'sqlite3': logging.WARNING,
        'watchfiles': logging.WARNING,  # 减少文件监控debug信息
        'mcp': logging.INFO,  # MCP相关日志
        'mcp.client': logging.INFO,
        'mcp.client.sse': logging.WARNING,  # 减少SSE连接debug信息
    }

    for logger_name, level in third_party_loggers.items():
        logging.getLogger(logger_name).setLevel(level)


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称，通常使用 __name__
        
    Returns:
        日志器实例
    """
    return logging.getLogger(name)


class StructuredLogger:
    """结构化日志器，支持添加额外的上下文信息"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.context: Dict[str, Any] = {}
    
    def add_context(self, **kwargs):
        """添加上下文信息"""
        self.context.update(kwargs)
    
    def clear_context(self):
        """清除上下文信息"""
        self.context.clear()
    
    def _log_with_context(self, level: int, message: str, **kwargs):
        """带上下文的日志记录"""
        extra = {**self.context, **kwargs}
        self.logger.log(level, message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        self._log_with_context(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        self._log_with_context(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self._log_with_context(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self._log_with_context(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        self._log_with_context(logging.CRITICAL, message, **kwargs)


def create_request_logger(request_id: str) -> StructuredLogger:
    """
    创建带请求ID的结构化日志器
    
    Args:
        request_id: 请求唯一标识
        
    Returns:
        结构化日志器实例
    """
    logger = get_logger("request")
    structured_logger = StructuredLogger(logger)
    structured_logger.add_context(request_id=request_id)
    return structured_logger


def log_performance(func_name: str, duration: float, **kwargs):
    """
    记录性能日志
    
    Args:
        func_name: 函数名称
        duration: 执行时间(秒)
        **kwargs: 额外的性能指标
    """
    logger = get_logger("performance")
    logger.info(
        f"性能指标 - {func_name}: {duration:.3f}s",
        extra={
            "function": func_name,
            "duration": duration,
            "timestamp": datetime.now().isoformat(),
            **kwargs
        }
    )


def log_user_action(user_id: str, action: str, details: Optional[Dict] = None):
    """
    记录用户行为日志
    
    Args:
        user_id: 用户ID
        action: 用户行为
        details: 行为详情
    """
    logger = get_logger("user_action")
    logger.info(
        f"用户行为 - {user_id}: {action}",
        extra={
            "user_id": user_id,
            "action": action,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        }
    )


# 预定义的日志配置
DEVELOPMENT_CONFIG = {
    "log_level": "DEBUG",
    "enable_console": True,
    "enable_colors": True,
    "log_file": None
}

PRODUCTION_CONFIG = {
    "log_level": "INFO",
    "enable_console": True,
    "enable_colors": False,
    "log_file": "app.log",
    "max_file_size": 50 * 1024 * 1024,  # 50MB
    "backup_count": 10
}

TESTING_CONFIG = {
    "log_level": "WARNING",
    "enable_console": False,
    "log_file": "test.log"
}


def setup_error_tracking():
    """设置错误追踪"""
    error_logger = get_logger("error_tracking")

    def handle_exception(exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        error_logger.critical(
            "未捕获的异常",
            exc_info=(exc_type, exc_value, exc_traceback),
            extra={
                "exception_type": exc_type.__name__,
                "exception_message": str(exc_value),
                "timestamp": datetime.now().isoformat()
            }
        )

    sys.excepthook = handle_exception


def create_performance_decorator(logger_name: str = "performance"):
    """
    创建性能监控装饰器

    Args:
        logger_name: 日志器名称

    Returns:
        性能监控装饰器
    """
    import time
    from functools import wraps

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                log_performance(func.__name__, duration, success=True)
                return result
            except Exception as e:
                duration = time.time() - start_time
                log_performance(func.__name__, duration, success=False, error=str(e))
                raise
        return wrapper
    return decorator


# 性能监控装饰器
monitor_performance = create_performance_decorator()
