{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "transport": "stdio"}, "tavily": {"command": "npx", "args": ["-y", "tavily-mcp@0.1.4"], "env": {"TAVILY_API_KEY": "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"}, "transport": "stdio"}, "Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"], "transport": "stdio"}, "Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "transport": "stdio"}, "mcp-server-chart": {"transport": "sse", "url": "https://mcp.api-inference.modelscope.net/06b0207b1f9b4c/sse"}}}