# mcp_loader.py
import json
import asyncio
from langchain_mcp_adapters.client import MultiServerMCPClient
from typing import List, Tuple, Optional
from langchain_core.tools import BaseTool
from utils.logging_config import get_logger

logger = get_logger(__name__)

async def load_mcp_tools_from_config(config_path: str = "config/mcp_config.json",
                                   timeout: int = 30,
                                   fallback_on_error: bool = True) -> Tuple[Optional[MultiServerMCPClient], List[BaseTool]]:
    """
    从指定的JSON配置文件加载并初始化MCP工具，带有错误处理和容错机制。

    Args:
        config_path (str): MCP工具配置文件的路径。
        timeout (int): 连接超时时间（秒）
        fallback_on_error (bool): 是否在错误时回退到空工具列表

    Returns:
        Tuple[Optional[MultiServerMCPClient], List[BaseTool]]: MCP客户端和LangChain兼容的工具列表。
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        logger.error(f"配置文件 '{config_path}' 未找到")
        if fallback_on_error:
            logger.warning("使用空工具列表继续运行")
            return None, []
        raise
    except json.JSONDecodeError as e:
        logger.error(f"无法解析配置文件 '{config_path}': {e}")
        if fallback_on_error:
            logger.warning("使用空工具列表继续运行")
            return None, []
        raise

    logger.info(f"正在从 '{config_path}' 加载MCP工具...")

    # 直接尝试加载所有服务器，让MultiServerMCPClient处理连接问题
    try:
        client = MultiServerMCPClient(config["mcpServers"])
        tools = await asyncio.wait_for(client.get_tools(), timeout=timeout)

        logger.info(f"成功加载 {len(tools)} 个MCP工具:")
        for tool in tools:
            logger.info(f"  - 工具名称: {tool.name}")
            logger.debug(f"    描述: {tool.description}")

        return client, tools

    except Exception as e:
        logger.error(f"加载MCP工具失败: {e}")
        if fallback_on_error:
            logger.warning("使用空工具列表继续运行")
            return None, []
        raise


async def load_mcp_tools_safe(config_path: str = "config/mcp_config.json") -> Tuple[Optional[MultiServerMCPClient], List[BaseTool]]:
    """
    安全加载MCP工具的简化接口，总是回退到空工具列表而不是抛出异常

    Args:
        config_path (str): MCP工具配置文件的路径

    Returns:
        Tuple[Optional[MultiServerMCPClient], List[BaseTool]]: MCP客户端和工具列表
    """
    try:
        return await load_mcp_tools_from_config(config_path, timeout=15, fallback_on_error=True)
    except Exception as e:
        logger.error(f"MCP工具加载完全失败: {e}")
        logger.warning("系统将在没有MCP工具的情况下运行")
        return None, []