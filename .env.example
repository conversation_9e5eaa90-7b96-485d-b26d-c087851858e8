# LangGraphAgent 环境配置示例文件
# 复制此文件为 .env 并根据需要修改配置值

# ===== 基础应用配置 =====
APP_ENVIRONMENT=development
APP_DEBUG_MODE=false
APP_HOST=localhost
APP_PORT=8000

# ===== 日志配置 =====
APP_LOGGING_LEVEL=INFO
APP_LOGGING_FILE=agent.log
APP_LOGGING_DIR=logs
APP_LOGGING_MAX_FILE_SIZE=52428800
APP_LOGGING_BACKUP_COUNT=10
APP_LOGGING_ENABLE_CONSOLE=true
APP_LOGGING_ENABLE_COLORS=true

# ===== LangSmith 配置 =====
# LangSmith 是 LangChain 的监控和调试平台
APP_LANGSMITH_TRACING_V2=false
APP_LANGSMITH_ENDPOINT=
APP_LANGSMITH_API_KEY=
APP_LANGSMITH_PROJECT=

# ===== 性能配置 =====
APP_PERFORMANCE_MAX_RETRIES=3
APP_PERFORMANCE_REQUEST_TIMEOUT=60
APP_PERFORMANCE_CONNECTION_POOL_SIZE=100
APP_PERFORMANCE_ENABLE_CACHING=true
APP_PERFORMANCE_CACHE_TTL=3600

# ===== 安全配置 =====
APP_SECURITY_ENABLE_AUTH=true
APP_SECURITY_ADMIN_PASSWORD=admin123
APP_SECURITY_SESSION_TIMEOUT=86400
APP_SECURITY_MAX_SESSIONS_PER_USER=10

# ===== 文件路径配置 =====
APP_CONFIG_DIR=config
APP_DATA_DIR=data
APP_LOGS_DIR=logs

# ===== Ollama 配置 =====
# Ollama 服务器配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.1:8b
OLLAMA_TIMEOUT=60
OLLAMA_MAX_RETRIES=3

# ===== Chainlit 配置 =====
# Chainlit 前端配置
CHAINLIT_HOST=localhost
CHAINLIT_PORT=8000
CHAINLIT_DEBUG=false
CHAINLIT_WATCH=true

# ===== 数据库配置 =====
# SQLite 数据库配置
DB_AGENT_DATA=agent_data.db
DB_AGENT_MEMORY=agent_memory.db
DB_CHAINLIT_HISTORY=chainlit_history.db

# ===== MCP 工具配置 =====
# Model Context Protocol 工具配置
MCP_TOOLS_ENABLED=true
MCP_CONFIG_FILE=config/mcp_config.json

# ===== 开发配置 =====
# 仅在开发环境使用
DEV_AUTO_RELOAD=true
DEV_SHOW_ERRORS=true
DEV_ENABLE_PROFILING=false

# ===== 生产配置 =====
# 仅在生产环境使用
PROD_ENABLE_METRICS=true
PROD_METRICS_PORT=9090
PROD_HEALTH_CHECK_INTERVAL=30
