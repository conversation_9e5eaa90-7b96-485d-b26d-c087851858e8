#!/usr/bin/env python3
"""
测试 create_react_agent 集成
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_create_react_agent():
    """测试新的 create_react_agent 实现"""
    try:
        print("🚀 开始测试 create_react_agent 集成...")
        
        # 导入主模块
        from main import initialize_agent
        from langchain_core.messages import HumanMessage
        
        # 初始化 Agent
        print("📝 初始化 Agent...")
        app, tools, session_manager = await initialize_agent()
        
        print(f"✅ Agent 初始化成功!")
        print(f"📊 工具数量: {len(tools)}")
        print(f"🔧 检查点类型: {type(app.checkpointer).__name__}")
        print(f"🎯 使用 create_react_agent: 是")
        
        # 测试简单对话
        print("\n💬 测试简单对话...")
        config = {"configurable": {"thread_id": "test_thread_001"}}
        
        test_message = HumanMessage(content="你好，请介绍一下你自己")
        
        try:
            result = await app.ainvoke(
                {"messages": [test_message]},
                config=config
            )
            
            print("✅ 对话测试成功!")
            print(f"📝 响应消息数量: {len(result['messages'])}")
            
            # 显示最后一条消息的内容（前100个字符）
            last_message = result['messages'][-1]
            content_preview = last_message.content[:100] + "..." if len(last_message.content) > 100 else last_message.content
            print(f"💭 响应预览: {content_preview}")
            
        except Exception as e:
            print(f"❌ 对话测试失败: {e}")
            return False
        
        # 测试工具调用（如果有工具的话）
        if tools:
            print(f"\n🔧 测试工具调用 (可用工具: {len(tools)} 个)...")
            tool_names = [tool.name for tool in tools]
            print(f"🛠️ 工具列表: {', '.join(tool_names[:3])}{'...' if len(tool_names) > 3 else ''}")
            
            # 这里可以添加具体的工具调用测试
            print("ℹ️ 工具调用测试跳过（需要具体的工具调用场景）")
        
        print("\n🎉 所有测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_create_react_agent())
    sys.exit(0 if success else 1)
